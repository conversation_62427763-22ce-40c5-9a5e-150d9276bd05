package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

func (p *DbPlugin) GetContent(ctx context.Context, subjectName string) ([]models.ContentBySubject, error) {
	start := time.Now()
	slog.Info("Retrieving content", "subject_name", subjectName)

	var subjects []models.Subject
	query := p.db.Preload("Chapters.Videos").Preload("Chapters.StudyMaterials")

	// If subject name is provided, filter by it; otherwise get all subjects
	if subjectName != "" {
		query = query.Where("name = ?", subjectName)
		slog.Debug("Filtering content by subject", "subject_name", subjectName)
	} else {
		slog.Debug("Retrieving content for all subjects")
	}

	if err := query.Find(&subjects).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve content",
			"subject_name", subjectName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve content: %w", err)
	}

	var result []models.ContentBySubject
	totalVideos := 0
	totalMaterials := 0

	for _, subject := range subjects {
		var chapters []models.Content

		for _, chapter := range subject.Chapters {
			// Convert Video to VideoForGet
			var videosForGet []models.VideoForGet
			for _, video := range chapter.Videos {
				videosForGet = append(videosForGet, models.VideoForGet{
					Name:        video.Name,
					DisplayName: video.DisplayName,
					VideoUrl:    video.VideoUrl,
					ViewCount:   video.ViewCount,
					ChapterID:   video.ChapterID,
				})
			}

			// Convert StudyMaterial to StudyMaterialForGet
			var materialsForGet []models.StudyMaterialForGet
			for _, material := range chapter.StudyMaterials {
				materialsForGet = append(materialsForGet, models.StudyMaterialForGet{
					Name:        material.Name,
					DisplayName: material.DisplayName,
					Url:         material.Url,
					ChapterID:   material.ChapterID,
				})
			}

			chapters = append(chapters, models.Content{
				ChapterName: chapter.Name,
				Videos:      videosForGet,
				Pdfs:        materialsForGet,
			})

			totalVideos += len(videosForGet)
			totalMaterials += len(materialsForGet)
		}

		result = append(result, models.ContentBySubject{
			SubjectName: subject.Name,
			Chapters:    chapters,
		})
	}

	duration := time.Since(start)
	slog.Info("Content retrieved successfully",
		"subject_name", subjectName,
		"subject_count", len(result),
		"total_videos", totalVideos,
		"total_materials", totalMaterials,
		"duration_ms", duration.Milliseconds(),
	)

	return result, nil
}

func (p *DbPlugin) AddVideo(ctx context.Context, video *models.Video, chapterName string) (*models.Video, error) {
	start := time.Now()
	slog.Info("Adding video to chapter",
		"video_name", video.Name,
		"video_display_name", video.DisplayName,
		"chapter_name", chapterName,
	)

	// Find the chapter by name
	var chapter models.Chapter
	if err := p.db.Where("name = ?", chapterName).First(&chapter).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Chapter not found for video addition",
			"video_name", video.Name,
			"chapter_name", chapterName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("chapter '%s' not found: %w", chapterName, err)
	}

	// Set the chapter ID
	video.ChapterID = chapter.ID

	slog.Debug("Creating video with chapter association",
		"video_name", video.Name,
		"chapter_id", chapter.ID,
		"chapter_name", chapterName,
	)

	res := p.db.Create(video)
	duration := time.Since(start)

	if res.Error != nil {
		slog.Error("Failed to create video",
			"video_name", video.Name,
			"chapter_name", chapterName,
			"chapter_id", chapter.ID,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	slog.Info("Video added successfully",
		"video_id", video.ID,
		"video_name", video.Name,
		"video_display_name", video.DisplayName,
		"chapter_name", chapterName,
		"chapter_id", chapter.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return video, nil
}

func (p *DbPlugin) AddStudyMaterial(ctx context.Context,
	pdf *models.StudyMaterial, chapterName string) (*models.StudyMaterial, error) {
	start := time.Now()
	slog.Info("Adding study material to chapter",
		"material_name", pdf.Name,
		"material_display_name", pdf.DisplayName,
		"chapter_name", chapterName,
	)

	// Find the chapter by name
	var chapter models.Chapter
	if err := p.db.Where("name = ?", chapterName).First(&chapter).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Chapter not found for study material addition",
			"material_name", pdf.Name,
			"chapter_name", chapterName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("chapter '%s' not found: %w", chapterName, err)
	}

	// Set the chapter ID
	pdf.ChapterID = chapter.ID

	slog.Debug("Creating study material with chapter association",
		"material_name", pdf.Name,
		"chapter_id", chapter.ID,
		"chapter_name", chapterName,
	)

	res := p.db.Create(pdf)
	duration := time.Since(start)

	if res.Error != nil {
		slog.Error("Failed to create study material",
			"material_name", pdf.Name,
			"chapter_name", chapterName,
			"chapter_id", chapter.ID,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	slog.Info("Study material added successfully",
		"material_id", pdf.ID,
		"material_name", pdf.Name,
		"material_display_name", pdf.DisplayName,
		"chapter_name", chapterName,
		"chapter_id", chapter.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return pdf, nil
}
