package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestGetContentAPI(t *testing.T) {
	// Skip if noAuthRouter is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping content API test")
		return
	}

	// Clean up before test
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	testSubjects := []string{
		"Physics Content Test " + timestamp,
		"Chemistry Content Test " + timestamp,
		"Mathematics Content Test " + timestamp,
	}

	// Clean up any existing test data
	for _, subjectName := range testSubjects {
		db.Exec("DELETE FROM videos WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM study_materials WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
	}

	// Create test subjects
	physicsSubject := models.Subject{
		Name:        testSubjects[0],
		DisplayName: "Physics Content Test Display",
	}
	db.Create(&physicsSubject)

	chemistrySubject := models.Subject{
		Name:        testSubjects[1],
		DisplayName: "Chemistry Content Test Display",
	}
	db.Create(&chemistrySubject)

	mathSubject := models.Subject{
		Name:        testSubjects[2],
		DisplayName: "Mathematics Content Test Display",
	}
	db.Create(&mathSubject)

	// Create test chapters
	physicsChapter1 := models.Chapter{
		Name:        "Mechanics " + timestamp,
		DisplayName: "Mechanics Display",
		SubjectID:   physicsSubject.ID,
	}
	db.Create(&physicsChapter1)

	physicsChapter2 := models.Chapter{
		Name:        "Thermodynamics " + timestamp,
		DisplayName: "Thermodynamics Display",
		SubjectID:   physicsSubject.ID,
	}
	db.Create(&physicsChapter2)

	chemistryChapter := models.Chapter{
		Name:        "Organic Chemistry " + timestamp,
		DisplayName: "Organic Chemistry Display",
		SubjectID:   chemistrySubject.ID,
	}
	db.Create(&chemistryChapter)

	mathChapter := models.Chapter{
		Name:        "Calculus " + timestamp,
		DisplayName: "Calculus Display",
		SubjectID:   mathSubject.ID,
	}
	db.Create(&mathChapter)

	// Create test videos
	physicsVideo1 := models.Video{
		Name:        "mechanics_intro_" + timestamp,
		DisplayName: "Introduction to Mechanics",
		VideoUrl:    "https://example.com/mechanics_intro.mp4",
		ViewCount:   150,
		ChapterID:   physicsChapter1.ID,
	}
	db.Create(&physicsVideo1)

	physicsVideo2 := models.Video{
		Name:        "thermodynamics_basics_" + timestamp,
		DisplayName: "Thermodynamics Basics",
		VideoUrl:    "https://example.com/thermodynamics_basics.mp4",
		ViewCount:   200,
		ChapterID:   physicsChapter2.ID,
	}
	db.Create(&physicsVideo2)

	chemistryVideo := models.Video{
		Name:        "organic_reactions_" + timestamp,
		DisplayName: "Organic Reactions",
		VideoUrl:    "https://example.com/organic_reactions.mp4",
		ViewCount:   100,
		ChapterID:   chemistryChapter.ID,
	}
	db.Create(&chemistryVideo)

	// Create test study materials
	physicsMaterial := models.StudyMaterial{
		Name:        "mechanics_notes_" + timestamp,
		DisplayName: "Mechanics Study Notes",
		Url:         "https://example.com/mechanics_notes.pdf",
		ChapterID:   physicsChapter1.ID,
	}
	db.Create(&physicsMaterial)

	chemistryMaterial := models.StudyMaterial{
		Name:        "organic_notes_" + timestamp,
		DisplayName: "Organic Chemistry Notes",
		Url:         "https://example.com/organic_notes.pdf",
		ChapterID:   chemistryChapter.ID,
	}
	db.Create(&chemistryMaterial)

	mathMaterial := models.StudyMaterial{
		Name:        "calculus_notes_" + timestamp,
		DisplayName: "Calculus Study Notes",
		Url:         "https://example.com/calculus_notes.pdf",
		ChapterID:   mathChapter.ID,
	}
	db.Create(&mathMaterial)

	// Test 1: Get all content (no subject filter)
	t.Run("GetAllContent", func(t *testing.T) {
		resp := requestExecutionHelper(http.MethodGet, "/api/content", nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var content []models.ContentBySubject
		err := json.Unmarshal(resp.Body.Bytes(), &content)
		assert.Nil(t, err)

		// Should return content for all subjects (at least our test subjects)
		assert.GreaterOrEqual(t, len(content), 3, "Should return content for at least our 3 test subjects")

		// Find our test subjects in the response
		subjectMap := make(map[string]models.ContentBySubject)
		for _, subjectContent := range content {
			subjectMap[subjectContent.SubjectName] = subjectContent
		}

		// Verify Physics content
		physicsContent, found := subjectMap[testSubjects[0]]
		assert.True(t, found, "Should find Physics subject in response")
		assert.Equal(t, 2, len(physicsContent.Chapters), "Physics should have 2 chapters")

		// Verify Chemistry content
		chemistryContent, found := subjectMap[testSubjects[1]]
		assert.True(t, found, "Should find Chemistry subject in response")
		assert.Equal(t, 1, len(chemistryContent.Chapters), "Chemistry should have 1 chapter")

		// Verify Mathematics content
		mathContent, found := subjectMap[testSubjects[2]]
		assert.True(t, found, "Should find Mathematics subject in response")
		assert.Equal(t, 1, len(mathContent.Chapters), "Mathematics should have 1 chapter")
	})

	// Test 2: Get content for specific subject (Physics)
	t.Run("GetPhysicsContent", func(t *testing.T) {
		url := fmt.Sprintf("/api/content?subject_name=%s", testSubjects[0])
		resp := requestExecutionHelper(http.MethodGet, url, nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var content []models.ContentBySubject
		err := json.Unmarshal(resp.Body.Bytes(), &content)
		assert.Nil(t, err)

		// Should return content for only Physics subject
		assert.Equal(t, 1, len(content), "Should return content for only 1 subject")
		assert.Equal(t, testSubjects[0], content[0].SubjectName, "Should return Physics subject")

		physicsContent := content[0]
		assert.Equal(t, 2, len(physicsContent.Chapters), "Physics should have 2 chapters")

		// Verify chapter structure and content
		chapterMap := make(map[string]models.Content)
		for _, chapter := range physicsContent.Chapters {
			chapterMap[chapter.ChapterName] = chapter
		}

		// Verify Mechanics chapter
		mechanicsChapter, found := chapterMap["Mechanics "+timestamp]
		assert.True(t, found, "Should find Mechanics chapter")
		assert.Equal(t, 1, len(mechanicsChapter.Videos), "Mechanics should have 1 video")
		assert.Equal(t, 1, len(mechanicsChapter.Pdfs), "Mechanics should have 1 study material")

		// Verify video structure (using VideoForGet)
		video := mechanicsChapter.Videos[0]
		assert.Equal(t, "mechanics_intro_"+timestamp, video.Name)
		assert.Equal(t, "Introduction to Mechanics", video.DisplayName)
		assert.Equal(t, "https://example.com/mechanics_intro.mp4", video.VideoUrl)
		assert.Equal(t, uint(150), video.ViewCount)
		assert.Equal(t, physicsChapter1.ID, video.ChapterID)

		// Verify study material structure (using StudyMaterialForGet)
		material := mechanicsChapter.Pdfs[0]
		assert.Equal(t, "mechanics_notes_"+timestamp, material.Name)
		assert.Equal(t, "Mechanics Study Notes", material.DisplayName)
		assert.Equal(t, "https://example.com/mechanics_notes.pdf", material.Url)
		assert.Equal(t, physicsChapter1.ID, material.ChapterID)

		// Verify Thermodynamics chapter
		thermoChapter, found := chapterMap["Thermodynamics "+timestamp]
		assert.True(t, found, "Should find Thermodynamics chapter")
		assert.Equal(t, 1, len(thermoChapter.Videos), "Thermodynamics should have 1 video")
		assert.Equal(t, 0, len(thermoChapter.Pdfs), "Thermodynamics should have 0 study materials")
	})

	// Test 3: Get content for non-existent subject
	t.Run("GetNonExistentSubjectContent", func(t *testing.T) {
		url := "/api/content?subject_name=NonExistentSubject"
		resp := requestExecutionHelper(http.MethodGet, url, nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var content []models.ContentBySubject
		err := json.Unmarshal(resp.Body.Bytes(), &content)
		assert.Nil(t, err)

		// Should return empty array for non-existent subject
		assert.Equal(t, 0, len(content), "Should return empty array for non-existent subject")
	})

	// Test 4: Get content for Chemistry subject
	t.Run("GetChemistryContent", func(t *testing.T) {
		url := fmt.Sprintf("/api/content?subject_name=%s", testSubjects[1])
		resp := requestExecutionHelper(http.MethodGet, url, nil)
		assert.Equal(t, http.StatusOK, resp.Code)

		var content []models.ContentBySubject
		err := json.Unmarshal(resp.Body.Bytes(), &content)
		assert.Nil(t, err)

		assert.Equal(t, 1, len(content), "Should return content for only 1 subject")
		assert.Equal(t, testSubjects[1], content[0].SubjectName, "Should return Chemistry subject")

		chemistryContent := content[0]
		assert.Equal(t, 1, len(chemistryContent.Chapters), "Chemistry should have 1 chapter")

		chapter := chemistryContent.Chapters[0]
		assert.Equal(t, "Organic Chemistry "+timestamp, chapter.ChapterName)
		assert.Equal(t, 1, len(chapter.Videos), "Should have 1 video")
		assert.Equal(t, 1, len(chapter.Pdfs), "Should have 1 study material")
	})

	// Cleanup
	for _, subjectName := range testSubjects {
		db.Exec("DELETE FROM videos WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM study_materials WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
	}
}
