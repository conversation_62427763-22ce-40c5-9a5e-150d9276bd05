time=2025-07-22T21:25:57.867+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T21:25:57.867+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T21:25:57.868+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T21:25:57.868+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T21:25:57.868+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T21:25:57.868+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T21:25:57.868+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T21:26:15.799+05:30 level=INFO msg="http: TLS handshake error from [::1]:57381: remote error: tls: unknown certificate"
time=2025-07-22T21:26:19.834+05:30 level=INFO msg="http: TLS handshake error from [::1]:57388: remote error: tls: unknown certificate"
time=2025-07-22T21:26:19.839+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:26:19.839+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/07/22 - 21:26:19 | 200 |            0s |             ::1 | GET      "/swagger/index.html"
time=2025-07-22T21:26:19.894+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:26:19.895+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:26:19.895+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:26:19.896+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=1 response_size=144966
[GIN] 2025/07/22 - 21:26:19 | 200 |      1.5484ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-22T21:26:19.897+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=2 response_size=312217
[GIN] 2025/07/22 - 21:26:19 | 200 |      2.1548ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-22T21:26:19.900+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=4 response_size=1061579
[GIN] 2025/07/22 - 21:26:19 | 200 |      5.6087ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-07-22T21:26:20.036+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:26:20.037+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=0 response_size=141752
[GIN] 2025/07/22 - 21:26:20 | 200 |         716µs |             ::1 | GET      "/swagger/doc.json"
time=2025-07-22T21:26:20.244+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:26:20.244+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/22 - 21:26:20 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-22T21:27:46.203+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T21:27:46.204+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T21:27:46.204+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T21:27:46.205+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T21:27:46.205+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T21:27:46.205+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T21:27:46.205+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T21:27:56.169+05:30 level=INFO msg="http: TLS handshake error from [::1]:57449: remote error: tls: unknown certificate"
time=2025-07-22T21:27:56.174+05:30 level=INFO msg="http: TLS handshake error from [::1]:57450: remote error: tls: unknown certificate"
time=2025-07-22T21:27:56.179+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:27:56.179+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/07/22 - 21:27:56 | 200 |       562.3µs |             ::1 | GET      "/swagger/index.html"
time=2025-07-22T21:27:56.195+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:27:56.195+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:27:56.195+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:27:56.196+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=1 response_size=144966
[GIN] 2025/07/22 - 21:27:56 | 200 |      1.0203ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-22T21:27:56.197+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=1 response_size=312217
[GIN] 2025/07/22 - 21:27:56 | 200 |      1.6172ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-22T21:27:56.199+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=3 response_size=1061579
[GIN] 2025/07/22 - 21:27:56 | 200 |      3.4834ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-07-22T21:27:56.304+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:27:56.305+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=1 response_size=145245
[GIN] 2025/07/22 - 21:27:56 | 200 |        1.02ms |             ::1 | GET      "/swagger/doc.json"
time=2025-07-22T21:27:56.310+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:27:56.310+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/22 - 21:27:56 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-22T21:33:16.162+05:30 level=INFO msg="http: TLS handshake error from [::1]:57615: remote error: tls: unknown certificate"
time=2025-07-22T21:33:16.167+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:33:16.168+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T21:33:17.287+05:30 level=INFO msg="Verification code sent successfully (stub)" phone_number=+919591592545 code_type=SMS client_ip=::1 duration_ms=1119
time=2025-07-22T21:33:17.288+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=1120 response_size=107
[GIN] 2025/07/22 - 21:33:17 | 200 |    1.1201381s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T21:42:42.991+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T21:42:42.991+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T21:42:42.991+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T21:42:42.991+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T21:42:42.992+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T21:42:42.992+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T21:42:42.992+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T21:43:31.605+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T21:43:31.607+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T21:43:31.607+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T21:43:31.607+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T21:43:31.608+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T21:43:31.608+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T21:43:31.608+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T21:43:40.799+05:30 level=INFO msg="http: TLS handshake error from [::1]:58015: remote error: tls: unknown certificate"
time=2025-07-22T21:43:40.810+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:43:40.811+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T21:43:41.633+05:30 level=INFO msg="Verification code sent successfully (stub)" phone_number=+919591592545 code_type=SMS client_ip=::1 duration_ms=821
time=2025-07-22T21:43:41.633+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=823 response_size=107
[GIN] 2025/07/22 - 21:43:41 | 200 |    823.1865ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T21:44:39.463+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T21:44:39.463+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T21:44:39.463+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T21:44:39.463+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T21:44:39.464+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T21:44:39.464+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T21:44:39.464+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T21:44:39.465+05:30 level=ERROR msg="HTTP server failed to start" address=:443 error="listen tcp :443: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted."
time=2025-07-22T21:44:42.465+05:30 level=INFO msg="initiating shutdown"
time=2025-07-22T21:44:56.521+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T21:44:56.522+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T21:44:56.522+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T21:44:56.522+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T21:44:56.523+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T21:44:56.523+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T21:44:56.523+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T21:45:12.957+05:30 level=INFO msg="http: TLS handshake error from [::1]:58106: remote error: tls: unknown certificate"
time=2025-07-22T21:45:12.966+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T21:45:12.967+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T21:45:13.990+05:30 level=INFO msg="Verification code sent successfully (stub)" phone_number=+919591592545 code_type=SMS client_ip=::1 duration_ms=1023
time=2025-07-22T21:45:13.991+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=1024 response_size=107
[GIN] 2025/07/22 - 21:45:13 | 200 |       1.0247s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T22:00:04.052+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:00:04.053+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:00:04.053+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:00:04.053+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:00:04.053+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:00:04.053+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:00:04.053+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:00:18.309+05:30 level=INFO msg="http: TLS handshake error from [::1]:58624: remote error: tls: unknown certificate"
time=2025-07-22T22:00:18.313+05:30 level=INFO msg="http: TLS handshake error from [::1]:58625: remote error: tls: unknown certificate"
time=2025-07-22T22:00:18.319+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:00:18.319+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/07/22 - 22:00:18 | 200 |            0s |             ::1 | GET      "/swagger/index.html"
time=2025-07-22T22:00:18.337+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:00:18.337+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=0 response_size=144966
[GIN] 2025/07/22 - 22:00:18 | 200 |       556.6µs |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-22T22:00:18.339+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:00:18.339+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:00:18.340+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=1 response_size=312217
[GIN] 2025/07/22 - 22:00:18 | 200 |      1.4361ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-22T22:00:18.343+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=3 response_size=1061579
[GIN] 2025/07/22 - 22:00:18 | 200 |       3.633ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-07-22T22:00:18.496+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:00:18.496+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:00:18.496+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/22 - 22:00:18 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-22T22:00:18.497+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=1 response_size=145245
[GIN] 2025/07/22 - 22:00:18 | 200 |      1.2198ms |             ::1 | GET      "/swagger/doc.json"
time=2025-07-22T22:00:32.481+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:00:32.481+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T22:00:33.678+05:30 level=INFO msg="Verification code sent successfully (stub)" phone_number=+919591592545 code_type=SMS client_ip=::1 duration_ms=1197
time=2025-07-22T22:00:33.678+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=1197 response_size=107
[GIN] 2025/07/22 - 22:00:33 | 200 |    1.1975072s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T22:17:46.686+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:17:46.688+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:17:46.688+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:17:46.689+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:17:46.689+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:17:46.689+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:17:46.689+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:17:52.466+05:30 level=INFO msg="http: TLS handshake error from [::1]:59393: remote error: tls: unknown certificate"
time=2025-07-22T22:17:52.469+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:17:52.470+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/07/22 - 22:17:52 | 200 |       515.6µs |             ::1 | GET      "/swagger/index.html"
time=2025-07-22T22:17:52.487+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:17:52.487+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:17:52.487+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:17:52.488+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=1 response_size=144966
[GIN] 2025/07/22 - 22:17:52 | 200 |      1.0838ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-22T22:17:52.489+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=2 response_size=312217
[GIN] 2025/07/22 - 22:17:52 | 200 |      2.1828ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-22T22:17:52.491+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=3 response_size=1061579
[GIN] 2025/07/22 - 22:17:52 | 200 |      3.7778ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-07-22T22:17:52.670+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:17:52.671+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:17:52.672+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/22 - 22:17:52 | 200 |       504.9µs |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-22T22:17:52.672+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=2 response_size=145245
[GIN] 2025/07/22 - 22:17:52 | 200 |      2.0592ms |             ::1 | GET      "/swagger/doc.json"
time=2025-07-22T22:18:05.872+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:18:05.872+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T22:18:06.901+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=1028 response_size=140
[GIN] 2025/07/22 - 22:18:06 | 500 |    1.0282785s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T22:19:13.864+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:19:13.864+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:19:13.864+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:19:13.864+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:19:13.864+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:19:13.864+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:19:13.864+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:19:24.283+05:30 level=INFO msg="http: TLS handshake error from [::1]:59416: remote error: tls: unknown certificate"
time=2025-07-22T22:19:24.289+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:19:24.290+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T22:19:25.511+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=1222 response_size=140
[GIN] 2025/07/22 - 22:19:25 | 500 |    1.2225374s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T22:24:12.975+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:24:12.977+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:24:12.977+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:24:12.977+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:24:12.977+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:24:12.977+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:24:12.977+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:24:12.977+05:30 level=ERROR msg="HTTP server failed to start" address=:443 error="listen tcp :443: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted."
time=2025-07-22T22:24:15.978+05:30 level=INFO msg="initiating shutdown"
time=2025-07-22T22:24:24.226+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:24:24.226+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:24:24.226+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:24:24.226+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:24:24.227+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:24:24.227+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:24:24.227+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:24:24.227+05:30 level=ERROR msg="HTTP server failed to start" address=:443 error="listen tcp :443: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted."
time=2025-07-22T22:24:27.228+05:30 level=INFO msg="initiating shutdown"
time=2025-07-22T22:24:50.448+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:24:50.449+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:24:50.449+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:24:50.449+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:24:50.449+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:24:50.449+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:24:50.449+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:24:55.941+05:30 level=INFO msg="http: TLS handshake error from [::1]:59560: remote error: tls: unknown certificate"
time=2025-07-22T22:24:55.948+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:24:55.948+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/07/22 - 22:24:55 | 200 |            0s |             ::1 | GET      "/swagger/index.html"
time=2025-07-22T22:24:55.966+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:24:55.966+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:24:55.966+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:24:55.967+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=1 response_size=144966
[GIN] 2025/07/22 - 22:24:55 | 200 |      1.1645ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-22T22:24:55.968+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=1 response_size=312217
[GIN] 2025/07/22 - 22:24:55 | 200 |      1.6669ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-22T22:24:55.969+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=3 response_size=1061579
[GIN] 2025/07/22 - 22:24:55 | 200 |      3.3543ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-07-22T22:24:56.117+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:24:56.118+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=1 response_size=145245
[GIN] 2025/07/22 - 22:24:56 | 200 |      1.0108ms |             ::1 | GET      "/swagger/doc.json"
time=2025-07-22T22:24:56.127+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:24:56.127+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/22 - 22:24:56 | 200 |       298.7µs |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-22T22:25:11.692+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:25:11.693+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T22:25:12.671+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=978 response_size=140
[GIN] 2025/07/22 - 22:25:12 | 500 |    978.5508ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T22:28:00.482+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:28:00.483+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:28:00.483+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:28:00.483+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:28:00.483+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:28:00.483+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:28:00.484+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:28:08.177+05:30 level=INFO msg="http: TLS handshake error from [::1]:59666: remote error: tls: unknown certificate"
time=2025-07-22T22:28:08.181+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:28:08.181+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T22:28:09.293+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=1111 response_size=140
[GIN] 2025/07/22 - 22:28:09 | 500 |    1.1114775s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T22:40:58.202+05:30 level=INFO msg="http: TLS handshake error from [::1]:60273: remote error: tls: unknown certificate"
time=2025-07-22T22:40:58.215+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:40:58.215+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T22:40:59.123+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=907 response_size=140
[GIN] 2025/07/22 - 22:40:59 | 500 |    907.7959ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T22:41:14.135+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:41:14.138+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:41:14.138+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:41:14.138+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:41:14.139+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:41:14.139+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:41:14.139+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:41:21.326+05:30 level=INFO msg="http: TLS handshake error from [::1]:60287: remote error: tls: unknown certificate"
time=2025-07-22T22:41:21.330+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:41:21.330+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T22:41:21.653+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=322 response_size=140
[GIN] 2025/07/22 - 22:41:21 | 500 |     322.621ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T22:41:27.511+05:30 level=INFO msg="http: TLS handshake error from [::1]:60292: remote error: tls: unknown certificate"
time=2025-07-22T22:41:27.521+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:41:27.521+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/07/22 - 22:41:27 | 200 |            0s |             ::1 | GET      "/swagger/index.html"
time=2025-07-22T22:41:27.537+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:41:27.537+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:41:27.537+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:41:27.538+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=1 response_size=144966
[GIN] 2025/07/22 - 22:41:27 | 200 |      1.0258ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-22T22:41:27.540+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=2 response_size=312217
[GIN] 2025/07/22 - 22:41:27 | 200 |      2.0953ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-22T22:41:27.541+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=3 response_size=1061579
[GIN] 2025/07/22 - 22:41:27 | 200 |      3.1309ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-07-22T22:41:27.701+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:41:27.701+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:41:27.701+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/22 - 22:41:27 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-22T22:41:27.702+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=1 response_size=145245
[GIN] 2025/07/22 - 22:41:27 | 200 |      1.2478ms |             ::1 | GET      "/swagger/doc.json"
time=2025-07-22T22:41:39.553+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:41:39.553+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T22:41:40.263+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=710 response_size=140
[GIN] 2025/07/22 - 22:41:40 | 500 |    710.0519ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T22:43:42.523+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:43:42.524+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:43:42.524+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:43:42.524+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:43:42.525+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:43:42.525+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:43:42.525+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:43:50.082+05:30 level=INFO msg="http: TLS handshake error from [::1]:60466: remote error: tls: unknown certificate"
time=2025-07-22T22:43:50.093+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:43:50.093+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T22:43:50.093+05:30 level=INFO msg="Twilio credentials" username=VA6c3072db106296c7b5f4b1d78e3610bd password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-22T22:43:51.174+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=1081 response_size=140
[GIN] 2025/07/22 - 22:43:51 | 500 |    1.0818127s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T22:44:17.036+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:44:17.037+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:44:17.037+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:44:17.037+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:44:17.037+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:44:17.037+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:44:17.037+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:44:23.569+05:30 level=INFO msg="http: TLS handshake error from [::1]:60487: remote error: tls: unknown certificate"
time=2025-07-22T22:44:23.580+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:44:23.580+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T22:44:23.580+05:30 level=INFO msg="Twilio credentials" username=VA6c3072db106296c7b5f4b1d78e3610bd password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-22T22:44:24.571+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=991 response_size=140
[GIN] 2025/07/22 - 22:44:24 | 500 |    991.0943ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T22:55:16.160+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:55:16.160+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:55:16.160+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:55:16.160+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:55:16.161+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:55:16.161+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:55:16.161+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:55:27.472+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:55:27.473+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:55:27.473+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:55:27.473+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:55:27.474+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:55:27.474+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:55:27.474+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:55:32.913+05:30 level=INFO msg="http: TLS handshake error from [::1]:61036: remote error: tls: unknown certificate"
time=2025-07-22T22:55:32.924+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:55:32.924+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T22:55:32.924+05:30 level=INFO msg="Twilio credentials" username=VA6c3072db106296c7b5f4b1d78e3610bd password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-22T22:55:34.027+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=1102 response_size=140
[GIN] 2025/07/22 - 22:55:34 | 500 |    1.1029955s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T22:58:22.141+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T22:58:22.142+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T22:58:22.142+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T22:58:22.142+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T22:58:22.142+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T22:58:22.142+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T22:58:22.142+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T22:58:32.699+05:30 level=INFO msg="http: TLS handshake error from [::1]:61246: remote error: tls: unknown certificate"
time=2025-07-22T22:58:32.703+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T22:58:32.704+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T22:58:32.704+05:30 level=INFO msg="Twilio credentials" username=VA6c3072db106296c7b5f4b1d78e3610bd password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-22T22:58:34.343+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=1639 response_size=147
[GIN] 2025/07/22 - 22:58:34 | 500 |    1.6392415s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T23:02:09.590+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T23:02:09.590+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T23:02:09.590+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T23:02:09.590+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T23:02:09.591+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T23:02:09.591+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T23:02:09.591+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T23:02:14.409+05:30 level=INFO msg="http: TLS handshake error from [::1]:61401: remote error: tls: unknown certificate"
time=2025-07-22T23:02:14.414+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T23:02:14.414+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T23:02:14.414+05:30 level=INFO msg="Twilio credentials" username=VA6c3072db106296c7b5f4b1d78e3610bd password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-22T23:02:15.505+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=1092 response_size=140
[GIN] 2025/07/22 - 23:02:15 | 500 |    1.0921846s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T23:05:14.881+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T23:05:14.882+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T23:05:14.882+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T23:05:14.882+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T23:05:14.882+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T23:05:14.882+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T23:05:14.882+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T23:05:20.380+05:30 level=INFO msg="http: TLS handshake error from [::1]:61507: remote error: tls: unknown certificate"
time=2025-07-22T23:05:20.382+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T23:05:20.382+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T23:05:20.382+05:30 level=INFO msg="Twilio credentials" username="" password=""
time=2025-07-22T23:05:21.376+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=993 response_size=147
[GIN] 2025/07/22 - 23:05:21 | 500 |     993.582ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T23:06:13.337+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T23:06:13.338+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T23:06:13.338+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T23:06:13.338+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T23:06:13.339+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T23:06:13.339+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T23:06:13.339+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T23:06:17.217+05:30 level=INFO msg="http: TLS handshake error from [::1]:61527: remote error: tls: unknown certificate"
time=2025-07-22T23:06:17.228+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T23:06:17.228+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T23:06:17.228+05:30 level=INFO msg="Twilio credentials" username="" password=""
time=2025-07-22T23:06:18.135+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=906 response_size=147
[GIN] 2025/07/22 - 23:06:18 | 500 |    906.8182ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T23:06:55.775+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T23:06:55.776+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T23:06:55.776+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T23:06:55.776+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T23:06:55.776+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T23:06:55.776+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T23:06:55.776+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T23:07:01.019+05:30 level=INFO msg="http: TLS handshake error from [::1]:61558: remote error: tls: unknown certificate"
time=2025-07-22T23:07:01.022+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T23:07:01.023+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T23:07:01.023+05:30 level=INFO msg="Twilio credentials" username="" password=""
time=2025-07-22T23:07:01.891+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=868 response_size=147
[GIN] 2025/07/22 - 23:07:01 | 500 |    868.8299ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-22T23:08:34.533+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-22T23:08:34.534+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-22T23:08:34.534+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-22T23:08:34.534+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-22T23:08:34.534+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-22T23:08:34.534+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-22T23:08:34.534+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-22T23:08:38.210+05:30 level=INFO msg="http: TLS handshake error from [::1]:61660: remote error: tls: unknown certificate"
time=2025-07-22T23:08:38.213+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-22T23:08:38.213+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-22T23:08:38.213+05:30 level=INFO msg="Twilio credentials" username=********************************** password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-22T23:08:39.422+05:30 level=INFO msg="Verification code sent successfully" phone_number=+919591592545 code_type=SMS client_ip=::1 duration_ms=1208
time=2025-07-22T23:08:39.422+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=1209 response_size=107
[GIN] 2025/07/22 - 23:08:39 | 200 |    1.2096015s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-23T09:49:23.685+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-23T09:49:23.685+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-23T09:49:23.685+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-23T09:49:23.686+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-23T09:49:23.686+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-23T09:49:23.686+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-23T09:49:23.686+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-23T09:49:28.825+05:30 level=INFO msg="http: TLS handshake error from [::1]:62344: remote error: tls: unknown certificate"
time=2025-07-23T09:49:28.830+05:30 level=INFO msg="http: TLS handshake error from [::1]:62345: remote error: tls: unknown certificate"
time=2025-07-23T09:49:28.836+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T09:49:28.836+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/07/23 - 09:49:28 | 200 |            0s |             ::1 | GET      "/swagger/index.html"
time=2025-07-23T09:49:28.857+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T09:49:28.857+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T09:49:28.857+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T09:49:28.858+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=1 response_size=144966
[GIN] 2025/07/23 - 09:49:28 | 200 |      1.0227ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-23T09:49:28.859+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=2 response_size=312217
[GIN] 2025/07/23 - 09:49:28 | 200 |      2.0752ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-23T09:49:28.860+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=3 response_size=1061579
[GIN] 2025/07/23 - 09:49:28 | 200 |      4.1423ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-07-23T09:49:29.034+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T09:49:29.035+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=1 response_size=145245
[GIN] 2025/07/23 - 09:49:29 | 200 |      1.2578ms |             ::1 | GET      "/swagger/doc.json"
time=2025-07-23T09:49:29.036+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T09:49:29.036+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/23 - 09:49:29 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-23T09:49:42.956+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T09:49:42.956+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-23T09:49:42.956+05:30 level=INFO msg="Twilio credentials" username=********************************** password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-23T09:49:45.149+05:30 level=ERROR msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=500 duration_ms=2192 response_size=124
[GIN] 2025/07/23 - 09:49:45 | 500 |    2.1924083s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-23T09:50:46.022+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-23T09:50:46.022+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-23T09:50:46.022+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-23T09:50:46.022+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-23T09:50:46.023+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-23T09:50:46.023+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-23T09:50:46.023+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-23T09:50:54.385+05:30 level=INFO msg="http: TLS handshake error from [::1]:62472: remote error: tls: unknown certificate"
time=2025-07-23T09:50:54.397+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T09:50:54.399+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-23T09:50:54.399+05:30 level=INFO msg="Twilio credentials" username=********************************** password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-23T09:50:56.563+05:30 level=INFO msg="Verification code sent successfully" phone_number=+919591592545 code_type=SMS client_ip=::1 duration_ms=2164
time=2025-07-23T09:50:56.563+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=2165 response_size=107
[GIN] 2025/07/23 - 09:50:56 | 200 |    2.1663822s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-23T18:17:05.639+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-23T18:17:05.644+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-23T18:17:05.644+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/students/verify-code --> ziaacademy-backend/cmd/server/http.(*Handlers).VerifyCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-23T18:17:05.644+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-23T18:17:05.645+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-23T18:17:05.645+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-23T18:17:05.645+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-23T18:17:33.641+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-23T18:17:33.642+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-23T18:17:33.642+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/students/verify-code --> ziaacademy-backend/cmd/server/http.(*Handlers).VerifyCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-23T18:17:33.642+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-23T18:17:33.643+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-23T18:17:33.643+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-23T18:17:33.643+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-23T18:17:37.393+05:30 level=INFO msg="http: TLS handshake error from [::1]:63902: remote error: tls: unknown certificate"
time=2025-07-23T18:17:37.399+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:17:37.399+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/07/23 - 18:17:37 | 200 |            0s |             ::1 | GET      "/swagger/index.html"
time=2025-07-23T18:17:37.418+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:17:37.418+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:17:37.418+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:17:37.419+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=1 response_size=144966
[GIN] 2025/07/23 - 18:17:37 | 200 |      1.1289ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-23T18:17:37.420+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=1 response_size=312217
[GIN] 2025/07/23 - 18:17:37 | 200 |      1.6313ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-23T18:17:37.422+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=3 response_size=1061579
[GIN] 2025/07/23 - 18:17:37 | 200 |      3.7548ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-07-23T18:17:37.568+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:17:37.569+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=1 response_size=147765
[GIN] 2025/07/23 - 18:17:37 | 200 |      1.0933ms |             ::1 | GET      "/swagger/doc.json"
time=2025-07-23T18:17:37.576+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:17:37.576+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/23 - 18:17:37 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-23T18:17:56.378+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:17:56.378+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-23T18:17:56.378+05:30 level=INFO msg="Twilio credentials" username=********************************** password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-23T18:17:57.694+05:30 level=INFO msg="Verification code sent successfully" phone_number=+919591592545 code_type=SMS client_ip=::1 duration_ms=1316
time=2025-07-23T18:17:57.694+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=1316 response_size=107
[GIN] 2025/07/23 - 18:17:57 | 200 |    1.3161988s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-23T18:18:41.390+05:30 level=INFO msg="Request started" method=POST path=/api/students/verify-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:18:41.390+05:30 level=INFO msg="Verification code check request" phone_number=+919591592545 client_ip=::1
time=2025-07-23T18:18:42.120+05:30 level=INFO msg="Verification check completed" phone_number=+919591592545 verified=true status=approved client_ip=::1 duration_ms=730
time=2025-07-23T18:18:42.120+05:30 level=INFO msg="Request completed" method=POST path=/api/students/verify-code client_ip=::1 status_code=200 duration_ms=730 response_size=76
[GIN] 2025/07/23 - 18:18:42 | 200 |    731.3707ms |             ::1 | POST     "/api/students/verify-code"
time=2025-07-23T18:19:08.461+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:19:08.461+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-23T18:19:08.461+05:30 level=INFO msg="Twilio credentials" username=********************************** password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-23T18:19:09.500+05:30 level=INFO msg="Verification code sent successfully" phone_number=+919591592545 code_type=SMS client_ip=::1 duration_ms=1038
time=2025-07-23T18:19:09.500+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=1038 response_size=107
[GIN] 2025/07/23 - 18:19:09 | 200 |    1.0385589s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-23T18:19:28.401+05:30 level=INFO msg="Request started" method=POST path=/api/students/verify-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:19:28.401+05:30 level=INFO msg="Verification code check request" phone_number=+919591592545 client_ip=::1
time=2025-07-23T18:19:29.102+05:30 level=INFO msg="Verification check completed" phone_number=+919591592545 verified=false status=pending client_ip=::1 duration_ms=701
time=2025-07-23T18:19:29.102+05:30 level=INFO msg="Request completed" method=POST path=/api/students/verify-code client_ip=::1 status_code=200 duration_ms=701 response_size=73
[GIN] 2025/07/23 - 18:19:29 | 200 |    701.5855ms |             ::1 | POST     "/api/students/verify-code"
time=2025-07-23T18:19:44.173+05:30 level=INFO msg="Request started" method=POST path=/api/students/verify-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:19:44.173+05:30 level=INFO msg="Verification code check request" phone_number=+919591592545 client_ip=::1
time=2025-07-23T18:19:44.924+05:30 level=INFO msg="Verification check completed" phone_number=+919591592545 verified=true status=approved client_ip=::1 duration_ms=750
time=2025-07-23T18:19:44.924+05:30 level=INFO msg="Request completed" method=POST path=/api/students/verify-code client_ip=::1 status_code=200 duration_ms=750 response_size=76
[GIN] 2025/07/23 - 18:19:44 | 200 |    750.9417ms |             ::1 | POST     "/api/students/verify-code"
time=2025-07-23T18:20:18.790+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:20:18.790+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-23T18:20:18.790+05:30 level=INFO msg="Twilio credentials" username=********************************** password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-23T18:20:19.661+05:30 level=INFO msg="Verification code sent successfully" phone_number=+919591592545 code_type=SMS client_ip=::1 duration_ms=870
time=2025-07-23T18:20:19.661+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=870 response_size=107
[GIN] 2025/07/23 - 18:20:19 | 200 |    870.8155ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-23T18:20:26.082+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:20:26.082+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-23T18:20:26.082+05:30 level=INFO msg="Twilio credentials" username=********************************** password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-23T18:20:26.894+05:30 level=INFO msg="Verification code sent successfully" phone_number=+919591592545 code_type=SMS client_ip=::1 duration_ms=812
time=2025-07-23T18:20:26.894+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=812 response_size=107
[GIN] 2025/07/23 - 18:20:26 | 200 |    812.0527ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-23T18:20:50.219+05:30 level=INFO msg="Request started" method=POST path=/api/students/verify-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:20:50.219+05:30 level=INFO msg="Verification code check request" phone_number=+919591592545 client_ip=::1
time=2025-07-23T18:20:50.499+05:30 level=INFO msg="Verification check completed" phone_number=+919591592545 verified=true status=approved client_ip=::1 duration_ms=279
time=2025-07-23T18:20:50.499+05:30 level=INFO msg="Request completed" method=POST path=/api/students/verify-code client_ip=::1 status_code=200 duration_ms=279 response_size=76
[GIN] 2025/07/23 - 18:20:50 | 200 |    279.9515ms |             ::1 | POST     "/api/students/verify-code"
time=2025-07-23T18:25:45.941+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:25:45.941+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-23T18:25:45.941+05:30 level=INFO msg="Twilio credentials" username=********************************** password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-23T18:25:46.920+05:30 level=INFO msg="Verification code sent successfully" phone_number=+919591592545 code_type=SMS client_ip=::1 duration_ms=979
time=2025-07-23T18:25:46.920+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=979 response_size=107
[GIN] 2025/07/23 - 18:25:46 | 200 |    979.0511ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-23T18:26:01.232+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:26:01.232+05:30 level=INFO msg="Verification code request" phone_number=+919739994848 code_type=SMS client_ip=::1
time=2025-07-23T18:26:01.232+05:30 level=INFO msg="Twilio credentials" username=********************************** password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-23T18:26:02.061+05:30 level=INFO msg="Verification code sent successfully" phone_number=+919739994848 code_type=SMS client_ip=::1 duration_ms=828
time=2025-07-23T18:26:02.061+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=828 response_size=107
[GIN] 2025/07/23 - 18:26:02 | 200 |    828.5259ms |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-23T18:26:29.002+05:30 level=INFO msg="Request started" method=POST path=/api/students/verify-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:26:29.003+05:30 level=INFO msg="Verification code check request" phone_number=+919591592545 client_ip=::1
time=2025-07-23T18:26:29.730+05:30 level=INFO msg="Verification check completed" phone_number=+919591592545 verified=true status=approved client_ip=::1 duration_ms=727
time=2025-07-23T18:26:29.730+05:30 level=INFO msg="Request completed" method=POST path=/api/students/verify-code client_ip=::1 status_code=200 duration_ms=727 response_size=76
[GIN] 2025/07/23 - 18:26:29 | 200 |    727.6437ms |             ::1 | POST     "/api/students/verify-code"
time=2025-07-23T18:26:47.412+05:30 level=INFO msg="Request started" method=POST path=/api/students/verify-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:26:47.412+05:30 level=INFO msg="Verification code check request" phone_number=+919739994848 client_ip=::1
time=2025-07-23T18:26:48.113+05:30 level=INFO msg="Verification check completed" phone_number=+919739994848 verified=true status=approved client_ip=::1 duration_ms=700
time=2025-07-23T18:26:48.113+05:30 level=INFO msg="Request completed" method=POST path=/api/students/verify-code client_ip=::1 status_code=200 duration_ms=700 response_size=76
[GIN] 2025/07/23 - 18:26:48 | 200 |    700.7749ms |             ::1 | POST     "/api/students/verify-code"
time=2025-07-23T18:38:09.780+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-23T18:38:09.781+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-23T18:38:09.781+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/students/verify-code --> ziaacademy-backend/cmd/server/http.(*Handlers).VerifyCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-23T18:38:09.781+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-23T18:38:09.781+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-23T18:38:09.781+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-23T18:38:09.781+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-23T18:38:30.942+05:30 level=INFO msg="http: TLS handshake error from [::1]:64827: remote error: tls: unknown certificate"
time=2025-07-23T18:38:30.955+05:30 level=INFO msg="Request started" method=POST path=/api/students/send-verification-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:38:30.956+05:30 level=INFO msg="Verification code request" phone_number=+919591592545 code_type=SMS client_ip=::1
time=2025-07-23T18:38:30.956+05:30 level=INFO msg="Twilio credentials" username=********************************** password=5beb07755d0e2c746575b8bacab96e1c
time=2025-07-23T18:38:32.211+05:30 level=INFO msg="Verification code sent successfully" phone_number=+919591592545 code_type=SMS client_ip=::1 duration_ms=1254
time=2025-07-23T18:38:32.211+05:30 level=INFO msg="Request completed" method=POST path=/api/students/send-verification-code client_ip=::1 status_code=200 duration_ms=1255 response_size=107
[GIN] 2025/07/23 - 18:38:32 | 200 |    1.2559184s |             ::1 | POST     "/api/students/send-verification-code"
time=2025-07-23T18:39:37.267+05:30 level=INFO msg="Request started" method=POST path=/api/students/verify-code client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-23T18:39:37.267+05:30 level=INFO msg="Verification code check request" phone_number=+919591592545 client_ip=::1
time=2025-07-23T18:39:38.012+05:30 level=INFO msg="Verification check completed" phone_number=+919591592545 verified=true status=approved client_ip=::1 duration_ms=744
time=2025-07-23T18:39:38.012+05:30 level=INFO msg="Request completed" method=POST path=/api/students/verify-code client_ip=::1 status_code=200 duration_ms=744 response_size=76
[GIN] 2025/07/23 - 18:39:38 | 200 |    744.5827ms |             ::1 | POST     "/api/students/verify-code"
time=2025-07-24T18:33:57.392+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-24T18:33:57.395+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-24T18:33:57.395+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/students/verify-code --> ziaacademy-backend/cmd/server/http.(*Handlers).VerifyCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-24T18:33:57.395+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-24T18:33:57.395+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-24T18:33:57.395+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-24T18:33:57.395+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-24T18:34:03.948+05:30 level=INFO msg="http: TLS handshake error from [::1]:60245: remote error: tls: unknown certificate"
time=2025-07-24T18:34:03.954+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:03.954+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/07/24 - 18:34:03 | 200 |       512.8µs |             ::1 | GET      "/swagger/index.html"
time=2025-07-24T18:34:03.970+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:03.970+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:03.970+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:03.970+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=0 response_size=144966
[GIN] 2025/07/24 - 18:34:03 | 200 |       503.4µs |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-24T18:34:03.971+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=1 response_size=312217
[GIN] 2025/07/24 - 18:34:03 | 200 |       1.536ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-24T18:34:03.972+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=2 response_size=1061579
[GIN] 2025/07/24 - 18:34:03 | 200 |      2.5745ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-07-24T18:34:04.129+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:04.130+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=1 response_size=147490
[GIN] 2025/07/24 - 18:34:04 | 200 |       1.014ms |             ::1 | GET      "/swagger/doc.json"
time=2025-07-24T18:34:04.132+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:04.132+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/24 - 18:34:04 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-24T18:34:21.404+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:21.404+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/07/24 - 18:34:21 | 200 |            0s |             ::1 | GET      "/swagger/index.html"
time=2025-07-24T18:34:21.422+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:21.422+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:21.422+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=304 duration_ms=0 response_size=-1
time=2025-07-24T18:34:21.422+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[GIN] 2025/07/24 - 18:34:21 | 304 |       194.7µs |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-24T18:34:21.422+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=304 duration_ms=0 response_size=-1
time=2025-07-24T18:34:21.422+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=304 duration_ms=0 response_size=-1
[GIN] 2025/07/24 - 18:34:21 | 304 |            0s |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
[GIN] 2025/07/24 - 18:34:21 | 304 |            0s |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-24T18:34:21.495+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:21.496+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:21.496+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/24 - 18:34:21 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-24T18:34:21.496+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=0 response_size=147490
[GIN] 2025/07/24 - 18:34:21 | 200 |       957.5µs |             ::1 | GET      "/swagger/doc.json"
time=2025-07-24T18:34:39.440+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-24T18:34:39.441+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-24T18:34:39.441+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/students/verify-code --> ziaacademy-backend/cmd/server/http.(*Handlers).VerifyCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-24T18:34:39.441+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-24T18:34:39.441+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-24T18:34:39.441+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-24T18:34:39.441+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-24T18:34:48.383+05:30 level=INFO msg="http: TLS handshake error from [::1]:60268: remote error: tls: unknown certificate"
time=2025-07-24T18:34:48.387+05:30 level=INFO msg="http: TLS handshake error from [::1]:60269: remote error: tls: unknown certificate"
time=2025-07-24T18:34:48.389+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:48.390+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/07/24 - 18:34:48 | 200 |       512.3µs |             ::1 | GET      "/swagger/index.html"
time=2025-07-24T18:34:48.410+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:48.410+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:48.410+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:48.411+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=0 response_size=144966
[GIN] 2025/07/24 - 18:34:48 | 200 |       699.3µs |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-24T18:34:48.412+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=1 response_size=312217
[GIN] 2025/07/24 - 18:34:48 | 200 |      1.9366ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-24T18:34:48.414+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=3 response_size=1061579
[GIN] 2025/07/24 - 18:34:48 | 200 |       3.617ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-07-24T18:34:48.464+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:48.465+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=0 response_size=146893
[GIN] 2025/07/24 - 18:34:48 | 200 |       504.1µs |             ::1 | GET      "/swagger/doc.json"
time=2025-07-24T18:34:48.469+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:34:48.469+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/24 - 18:34:48 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-24T18:35:11.982+05:30 level=INFO msg="Request started" method=POST path=/api/login client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:35:11.982+05:30 level=INFO msg="Login attempt" email=<EMAIL> client_ip=::1
time=2025-07-24T18:35:12.089+05:30 level=ERROR msg="Failed to retrieve student by user ID" user_id=21 error="record not found" duration_ms=5
time=2025-07-24T18:35:12.089+05:30 level=ERROR msg="Login failed - student retrieval error" email=<EMAIL> user_id=21 client_ip=::1 error="record not found" duration_ms=106
time=2025-07-24T18:35:12.089+05:30 level=ERROR msg="Request completed" method=POST path=/api/login client_ip=::1 status_code=500 duration_ms=106 response_size=46
[GIN] 2025/07/24 - 18:35:12 | 500 |     106.895ms |             ::1 | POST     "/api/login"
time=2025-07-24T18:42:11.577+05:30 level=INFO msg="Request started" method=POST path=/api/students client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:42:11.577+05:30 level=INFO msg="Creating student" email=<EMAIL>
time=2025-07-24T18:42:11.588+05:30 level=INFO msg="Student created successfully" email=<EMAIL> student_id=541 duration_ms=11
time=2025-07-24T18:42:11.589+05:30 level=INFO msg="Request completed" method=POST path=/api/students client_ip=::1 status_code=200 duration_ms=11 response_size=196
[GIN] 2025/07/24 - 18:42:11 | 200 |     11.2935ms |             ::1 | POST     "/api/students"
time=2025-07-24T18:43:03.238+05:30 level=INFO msg="Request started" method=POST path=/api/users/password client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:43:03.238+05:30 level=INFO msg="Updating user password" user_id=900
time=2025-07-24T18:43:03.326+05:30 level=INFO msg="Password updated successfully" user_id=900 email=<EMAIL> duration_ms=87
time=2025-07-24T18:43:03.327+05:30 level=INFO msg="Request completed" method=POST path=/api/users/password client_ip=::1 status_code=200 duration_ms=89 response_size=2
[GIN] 2025/07/24 - 18:43:03 | 200 |     89.4945ms |             ::1 | POST     "/api/users/password"
time=2025-07-24T18:44:11.758+05:30 level=INFO msg="Request started" method=GET path=/api/test-types client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:44:11.758+05:30 level=INFO msg="GetTestTypes request started" client_ip=::1 method=GET path=/api/test-types
time=2025-07-24T18:44:11.758+05:30 level=INFO msg="Retrieving all test types"
time=2025-07-24T18:44:11.762+05:30 level=INFO msg="Test types retrieved successfully" test_type_count=37 duration_ms=4
time=2025-07-24T18:44:11.762+05:30 level=INFO msg="GetTestTypes completed successfully" client_ip=::1 test_type_count=37 duration_ms=4
time=2025-07-24T18:44:11.772+05:30 level=INFO msg="Request completed" method=GET path=/api/test-types client_ip=::1 status_code=200 duration_ms=13 response_size=5808
[GIN] 2025/07/24 - 18:44:11 | 200 |     13.8633ms |             ::1 | GET      "/api/test-types"
time=2025-07-24T18:45:14.950+05:30 level=INFO msg="Request started" method=POST path=/api/tests client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:45:14.959+05:30 level=INFO msg="Request completed" method=POST path=/api/tests client_ip=::1 status_code=200 duration_ms=9 response_size=37
[GIN] 2025/07/24 - 18:45:14 | 200 |      9.6626ms |             ::1 | POST     "/api/tests"
time=2025-07-24T18:45:26.369+05:30 level=INFO msg="Request started" method=GET path=/api/tests client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:45:26.374+05:30 level=INFO msg="GetTests database operation completed" user_id=900 role=Student test_count=1 active_filter=<nil> duration_ms=4
time=2025-07-24T18:45:26.374+05:30 level=INFO msg="GetTests successful" user_id=900 client_ip=::1 active_filter="" test_count=1 duration_ms=4
time=2025-07-24T18:45:26.374+05:30 level=INFO msg="Request completed" method=GET path=/api/tests client_ip=::1 status_code=200 duration_ms=4 response_size=4169
[GIN] 2025/07/24 - 18:45:26 | 200 |      4.9297ms |             ::1 | GET      "/api/tests"
time=2025-07-24T18:47:38.810+05:30 level=INFO msg="Request started" method=POST path=/api/login client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:47:38.810+05:30 level=INFO msg="Login attempt" email=<EMAIL> client_ip=::1
time=2025-07-24T18:47:38.811+05:30 level=WARN msg="User not found during password validation" email=<EMAIL> error="record not found" duration_ms=0
time=2025-07-24T18:47:38.811+05:30 level=WARN msg="Login failed - invalid credentials" email=<EMAIL> client_ip=::1 error="record not found" duration_ms=1
time=2025-07-24T18:47:38.811+05:30 level=WARN msg="Request completed" method=POST path=/api/login client_ip=::1 status_code=401 duration_ms=1 response_size=31
[GIN] 2025/07/24 - 18:47:38 | 401 |      1.0978ms |             ::1 | POST     "/api/login"
time=2025-07-24T18:52:02.350+05:30 level=INFO msg="Request started" method=POST path=/api/admins client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:52:02.350+05:30 level=INFO msg="Creating admin user" email=<EMAIL> full_name="F name"
time=2025-07-24T18:52:02.431+05:30 level=INFO msg="Admin user created successfully" admin_id=901 email=<EMAIL> full_name="F name" duration_ms=80
time=2025-07-24T18:52:02.431+05:30 level=INFO msg="Request completed" method=POST path=/api/admins client_ip=::1 status_code=201 duration_ms=80 response_size=195
[GIN] 2025/07/24 - 18:52:02 | 201 |     80.6833ms |             ::1 | POST     "/api/admins"
time=2025-07-24T18:52:24.840+05:30 level=INFO msg="Request started" method=POST path=/api/login client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:52:24.840+05:30 level=INFO msg="Login attempt" email=<EMAIL> client_ip=::1
time=2025-07-24T18:52:24.935+05:30 level=ERROR msg="Failed to retrieve student by user ID" user_id=901 error="record not found" duration_ms=0
time=2025-07-24T18:52:24.935+05:30 level=ERROR msg="Login failed - student retrieval error" email=<EMAIL> user_id=901 client_ip=::1 error="record not found" duration_ms=95
time=2025-07-24T18:52:24.935+05:30 level=ERROR msg="Request completed" method=POST path=/api/login client_ip=::1 status_code=500 duration_ms=95 response_size=46
[GIN] 2025/07/24 - 18:52:24 | 500 |     95.7705ms |             ::1 | POST     "/api/login"
time=2025-07-24T18:55:10.265+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-24T18:55:10.266+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-24T18:55:10.266+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (4 handlers)
[GIN-debug] POST   /api/students/verify-code --> ziaacademy-backend/cmd/server/http.(*Handlers).VerifyCode-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-24T18:55:10.266+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-24T18:55:10.266+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-24T18:55:10.266+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-24T18:55:10.266+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-24T18:55:23.695+05:30 level=INFO msg="http: TLS handshake error from [::1]:60459: remote error: tls: unknown certificate"
time=2025-07-24T18:55:23.707+05:30 level=INFO msg="Request started" method=POST path=/api/login client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:55:23.708+05:30 level=INFO msg="Login attempt" email=<EMAIL> client_ip=::1
time=2025-07-24T18:55:23.765+05:30 level=INFO msg="Login successful" email=<EMAIL> user_id=901 client_ip=::1 duration_ms=58
time=2025-07-24T18:55:23.765+05:30 level=INFO msg="Request completed" method=POST path=/api/login client_ip=::1 status_code=200 duration_ms=58 response_size=160
[GIN] 2025/07/24 - 18:55:23 | 200 |     58.8981ms |             ::1 | POST     "/api/login"
time=2025-07-24T18:55:58.332+05:30 level=INFO msg="Request started" method=GET path=/api/tests client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:55:58.346+05:30 level=INFO msg="GetTests database operation completed" user_id=901 role=Admin test_count=2 active_filter=<nil> duration_ms=14
time=2025-07-24T18:55:58.346+05:30 level=INFO msg="GetTests successful" user_id=901 client_ip=::1 active_filter="" test_count=2 duration_ms=14
time=2025-07-24T18:55:58.346+05:30 level=INFO msg="Request completed" method=GET path=/api/tests client_ip=::1 status_code=200 duration_ms=14 response_size=4631
[GIN] 2025/07/24 - 18:55:58 | 200 |     14.4467ms |             ::1 | GET      "/api/tests"
time=2025-07-24T18:58:21.726+05:30 level=INFO msg="Request started" method=GET path=/api/test-types client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:58:21.726+05:30 level=INFO msg="GetTestTypes request started" client_ip=::1 method=GET path=/api/test-types
time=2025-07-24T18:58:21.726+05:30 level=INFO msg="Retrieving all test types"
time=2025-07-24T18:58:21.729+05:30 level=INFO msg="Test types retrieved successfully" test_type_count=37 duration_ms=2
time=2025-07-24T18:58:21.729+05:30 level=INFO msg="GetTestTypes completed successfully" client_ip=::1 test_type_count=37 duration_ms=2
time=2025-07-24T18:58:21.729+05:30 level=INFO msg="Request completed" method=GET path=/api/test-types client_ip=::1 status_code=200 duration_ms=2 response_size=5808
[GIN] 2025/07/24 - 18:58:21 | 200 |      2.9506ms |             ::1 | GET      "/api/test-types"
time=2025-07-24T18:58:49.825+05:30 level=INFO msg="Request started" method=GET path=/api/section-types client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:58:49.825+05:30 level=INFO msg="GetSectionTypes request started" client_ip=::1 method=GET path=/api/section-types
time=2025-07-24T18:58:49.825+05:30 level=INFO msg="Retrieving all section types"
time=2025-07-24T18:58:49.830+05:30 level=INFO msg="Section types retrieved successfully" section_type_count=4 duration_ms=4
time=2025-07-24T18:58:49.830+05:30 level=INFO msg="GetSectionTypes completed successfully" client_ip=::1 section_type_count=4 duration_ms=4
time=2025-07-24T18:58:49.830+05:30 level=INFO msg="Request completed" method=GET path=/api/section-types client_ip=::1 status_code=200 duration_ms=4 response_size=1706
[GIN] 2025/07/24 - 18:58:49 | 200 |      4.8584ms |             ::1 | GET      "/api/section-types"
time=2025-07-24T18:59:56.826+05:30 level=INFO msg="Request started" method=POST path=/api/test-types client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T18:59:56.847+05:30 level=INFO msg="Request completed" method=POST path=/api/test-types client_ip=::1 status_code=200 duration_ms=20 response_size=31
[GIN] 2025/07/24 - 18:59:56 | 200 |     20.8288ms |             ::1 | POST     "/api/test-types"
time=2025-07-24T19:00:31.245+05:30 level=INFO msg="Request started" method=POST path=/api/tests client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T19:00:31.253+05:30 level=INFO msg="Request completed" method=POST path=/api/tests client_ip=::1 status_code=200 duration_ms=7 response_size=38
[GIN] 2025/07/24 - 19:00:31 | 200 |      8.4322ms |             ::1 | POST     "/api/tests"
time=2025-07-24T19:00:37.864+05:30 level=INFO msg="Request started" method=GET path=/api/tests client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-24T19:00:37.866+05:30 level=INFO msg="GetTests database operation completed" user_id=901 role=Admin test_count=3 active_filter=<nil> duration_ms=2
time=2025-07-24T19:00:37.866+05:30 level=INFO msg="GetTests successful" user_id=901 client_ip=::1 active_filter="" test_count=3 duration_ms=2
time=2025-07-24T19:00:37.866+05:30 level=INFO msg="Request completed" method=GET path=/api/tests client_ip=::1 status_code=200 duration_ms=2 response_size=6996
[GIN] 2025/07/24 - 19:00:37 | 200 |      2.2969ms |             ::1 | GET      "/api/tests"
