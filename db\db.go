package db

import (
	"context"
	"ziaacademy-backend/internal/models"

	"gorm.io/gorm"
)

type DbPlugin struct {
	db *gorm.DB
}

// Server has all the methods required to run the server
type Server interface {
	CreateStudent(ctx context.Context, user *models.Student) (*models.Student, error)
	CreateAdmin(ctx context.Context, admin *models.User) (*models.User, error)
	CreateCourse(ctx context.Context, course *models.Course) (*models.Course, error)
	CreateSubject(ctx context.Context, subject *models.Subject) (*models.Subject, error)
	GetSubjects(ctx context.Context, student_id uint) ([]models.Subject, error)
	GetChapters(ctx context.Context, subject_id uint) ([]models.Chapter, error)
	GetContent(ctx context.Context, chapter_id uint) (*models.Content, error)
	GetCourses(ctx context.Context,
		userID uint) (*models.CoursesByCategory, error)
	ValidateUserPassword(ctx context.Context, userEmail, pwd string) (uint, error)
	UpdatePassword(ctx context.Context, userID uint, newPwd string) error
	AddVideo(ctx context.Context, video *models.Video, chapterName string) (*models.Video, error)
	AddStudyMaterial(ctx context.Context,
		pdf *models.StudyMaterial, chapterName string) (*models.StudyMaterial, error)
	CreateChapter(ctx context.Context, chapter *models.Chapter, subjectName string) (*models.Chapter, error)
	CreateTopic(ctx context.Context, topic *models.Topic, chapterName string) (*models.Topic, error)
	GetTopics(ctx context.Context, chapterName string) ([]models.Topic, error)
	CreateQuestion(ctx context.Context, question *models.Question, topicName, difficultyName string, options []models.OptionForCreate) (*models.Question, error)
	GetQuestions(ctx context.Context, topicName,
		difficulty string) ([]models.Question, error)
	EnrollStudentInCourse(ctx context.Context, userID,
		courseID uint) (*models.Student, error)
	GetStudentByUserID(ctx context.Context,
		userID uint) (*models.StudentForCreate, error)
	GetStudentIDByUserID(ctx context.Context, userID uint) (uint, error)
	CreateSectionType(ctx context.Context, sectionType *models.SectionType, subjectName string) (*models.SectionType, error)
	CreateTestType(ctx context.Context, testType *models.TestType, sectionTypeNames []string) (*models.TestType, error)
	CreateTest(ctx context.Context, test *models.Test, testTypeName string) (*models.Test, error)
	AddQuestionsToTest(ctx context.Context, testID uint, questionIDs []uint, sectionName string) error
	RemoveQuestionsFromTest(ctx context.Context, testID uint, questionIDs []uint, sectionName string) error
	AssociateTestWithCourse(ctx context.Context, courseID, testID uint) error
	GetTests(ctx context.Context, userID uint, activeOnly *bool) ([]models.TestForGet, error)
	GetTestTypes(ctx context.Context) ([]models.TestType, error)
	GetSectionTypes(ctx context.Context) ([]models.SectionType, error)
	ToggleTestActiveStatus(ctx context.Context, testID uint) error
	UpdateTestEvaluationStatus(ctx context.Context, testID uint, status models.EvaluationStatus) error
	RecordTestResponses(ctx context.Context, studentID uint, testResponsesInput *models.TestResponsesForCreate) (*models.TestResponsesResult, error)
	GetStudentTestResponses(ctx context.Context, studentID, testID uint) (*models.StudentTestResponsesResult, error)
	EvaluateTestResponses(ctx context.Context, testID uint) (*models.TestEvaluationResult, error)
	GetTestRankings(ctx context.Context, testID uint, limit, offset int) (*models.TestRankingResult, error)
	CreateFormulaCards(ctx context.Context, formulaCardsInput *models.FormulaCardsForCreate) ([]models.FormulaCard, error)
	GetAllFormulaCardsOrganizedBySubject(ctx context.Context) ([]models.FormulaCardsBySubject, error)
	CreatePreviousYearPapers(ctx context.Context, papersInput *models.PreviousYearPapersForCreate) ([]models.PreviousYearPaper, error)
	GetPreviousYearPapersByExamType(ctx context.Context, examType string) ([]models.PreviousYearPaper, error)
	GetAllPreviousYearPapersOrganizedByExamType(ctx context.Context) ([]models.PreviousYearPapersByExamType, error)

	// Comment-related methods
	AddComment(ctx context.Context, comment *models.Comment) (*models.Comment, error)
	AddResponse(ctx context.Context, response *models.Response) (*models.Response, error)
	GetCommentsForVideo(ctx context.Context, videoID uint, userID uint) (*models.CommentsResponse, error)
	GetCommentsForMaterial(ctx context.Context, materialID uint, userID uint) (*models.CommentsResponse, error)

	// Transaction-related methods
	CreateTransaction(ctx context.Context, transaction *models.Transaction, courseIDs []uint) (*models.Transaction, error)
	UpdateTransactionStatus(ctx context.Context, transactionID uint, status, paymentReference string) error
	GetStudentTransactions(ctx context.Context, studentID uint) ([]models.Transaction, error)
	GetTransactionByID(ctx context.Context, transactionID uint) (*models.Transaction, error)
	GetCompletedTransactionsByStudent(ctx context.Context, studentID uint) ([]models.Transaction, error)
	EnrollStudentInPurchasedCourses(ctx context.Context, transactionID uint) error
}

func NewServer(db *gorm.DB) Server {
	return &DbPlugin{
		db: db,
	}
}
func NewDbPlugin(db *gorm.DB) *DbPlugin {
	return &DbPlugin{
		db: db,
	}
}
